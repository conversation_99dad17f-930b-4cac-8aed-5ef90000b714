import { getTranslations } from 'next-intl/server';

import AlphaTester from './alpha-tester';
import Contact from './contact';
import Features from './features';
import Partner from './partner';

import CloudComputing from './cloud-computing';

import EfficientService from './efficient-service';
import TierPrice from './tier-price';

import MainBanner from './main-banner';

// type Props = {
//   params: >;
// };

export default async function IndexPage(params: Promise<{ locale: string }>) {
  const { locale } = await params;

  const t_i18n = await getTranslations('i18nData');

  const indexTitle = () => (
    <div>
      <div className="flex w-full justify-center lg:!justify-start">
        <h3 className="text-[20px] font-bold text-[#78829d]"> Global GPU Grid</h3>
      </div>
      <div className="flex justify-center lg:!justify-start">
        <h3
          className="text-[38px] font-bold md:text-[50px] lg:text-[55px] xl:text-[70px]"
          dangerouslySetInnerHTML={{ __html: t_i18n.raw('index_msg01') }}
        ></h3>
      </div>
    </div>
  );

  return (
    <>
      {/* <div className="container-fixed bg-[url('/da/img/leanding/sector_01_bg.png')] bg-contain bg-top bg-no-repeat py-7.5">
        <div className="lg:gap-none grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="order-2 col-span-1 flex items-center justify-center lg:order-1">
            <div className="flex flex-col gap-5">
              <div className="hidden lg:block">{indexTitle()}</div>
              <div className="flex flex-col text-center lg:items-start lg:!justify-start lg:text-left">
                <span className="text-[22px] font-medium leading-[38px] tracking-[-1%] text-primary">{t_i18n('index_msg02')}</span>
                <span className="text-[1rem] font-medium leading-[30px] text-gray-600 lg:text-[18px]">{t_i18n('index_msg03')}</span>
              </div>
              <div className="flex justify-start gap-7">
                <div className="flex justify-center gap-2 py-7">
                  <div className="mr-3 flex flex-col gap-2">
                    <span
                      className="max-w-[556px] text-[0.9rem] lg:text-[1rem]"
                      dangerouslySetInnerHTML={{ __html: t_i18n.raw('index_msg031') }}
                    ></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="order-1 col-span-1 grid lg:order-2">
            <div className="aspect-w-16 aspect-h-9">
              <img src={'/da/img/leanding/sector_01.png'} className="object-contain lg:w-[700px]" alt="" />
            </div>
          </div>
        </div>
      </div> */}

      <MainBanner />

      <EfficientService />

      {/* <GcubeVideo /> */}

      <CloudComputing />

      {/* <TechnologicalAdvantage /> */}

      <Features />

      <TierPrice />

      <AlphaTester />

      {/* <Blog /> */}

      {/* <DeveloperComment /> */}

      <Partner />

      <Contact />
      {/* <PopupPage></PopupPage> */}
    </>
  );
}
