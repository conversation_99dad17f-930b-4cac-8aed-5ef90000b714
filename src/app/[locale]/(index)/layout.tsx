import '@/styles/globals.css';
import '@/styles/styles-gcube.css';

import dynamic from 'next/dynamic';
import IndexFooter from './index-footer';
import IndexHeader from './index-header';

import KakaoChannelButton from '@/components/kakao/kakao-channel-button';
import { Metadata } from 'next';
import localFont from 'next/font/local';
import { headers } from 'next/headers';

const GcubeBanner = dynamic(() => import('@/components/banner/gcube-banner'));

const pretendard = localFont({
  src: '../../fonts/PretendardVariable.woff2',
  weight: '45 920',
  display: 'swap'
});

const GlobalInit = dynamic(() => import('@/components/GlobalInit'), {
  ssr: false
});

export function generateMetadata(): Metadata {
  const headersList = headers();

  const canonicalUrl = headersList.get('canonical') ?? '/';
  return {
    metadataBase: new URL('https://gcube.ai'),
    title: 'gcube | 지큐브 | 클라우드 GPU',
    description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.',
    keywords: ['GCUBE', 'gcube', '지큐브', 'NVIDIA GPU', 'AI'],
    openGraph: {
      title: 'gcube | 지큐브 | 클라우드 GPU',
      description: 'AI 학습과 추론에 필수인 GPU! 지큐브에 무엇이든 물어보세요. NVIDIA GPU를 합리적인 가격으로 원할 때 사용 가능합니다.'
    },
    alternates: {
      canonical: canonicalUrl
    }
  };
}

/**
 * @brief 인트로용 layout
 * @param param
 * @returns
 */
export default async function MainLayout({
  children,
  params: { locale }
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  return (
    <>
      <body className={`${pretendard.className} flex overflow-auto`}>
        <div className="flex grow">
          <div className="wrapper flex grow flex-col" style={{ left: 0, paddingTop: 0 }}>
            {/* <GcubeBanner></GcubeBanner> */}

            <IndexHeader></IndexHeader>
            <main className="content grow" id="content" role="content">
              {children}
            </main>
            <IndexFooter></IndexFooter>
          </div>
        </div>
        <GlobalInit />
        <KakaoChannelButton></KakaoChannelButton>

        <div id="global_modal"></div>
      </body>
    </>
  );
}
